# Free-Train 项目说明

Free-Train 包含一组用于构建和评估 **Free Think** 对话模型的数据处理脚本。项目主要负责：

- 从 MongoDB 提取并清洗聊天记录；
- 计算用户行为评分；
- 生成 SFT 或 KTO 训练/对比数据集；
- 通过 LangSmith 评测模型效果。

## 运行环境

1. Python 3.10 及以上
2. 拥有聊天数据的 MongoDB 数据库
3. 在 `.env` 文件中配置 LangSmith 及模型服务的 API 密钥

安装依赖：

```bash
pip install -r requirements.txt
```

## 目录结构

```
code/
├── action_plot.py          # 获取用户数据并绘制元行为时间轴
├── chat_history.py         # 合并聊天记录与状态标签并计算评分
├── strategy_plot.py        # 思考策略长度直方图
├── utils.py                # 相关性分析与聊天记录工具
├── mongodb/
│   ├── base.py             # MongoDB 客户端封装
│   └── get_data_from_db.py # 获取数据库示例
├── dataset/
│   ├── dataset.py          # 依据 LangSmith 运行记录生成 SFT/KTO 数据集
│   ├── get_model.py        # 下载并调用 Qwen 模型
│   ├── langsmith_data.py   # 导出 LangSmith 运行数据
│   ├── langsmith_eval.py   # LangSmith 单模型/双模型评测
│   ├── load_human_data.py  # 处理人工聊天记录
│   ├── cleaning.py         # 解码 base64 格式的聊天文本
│   └── local_chat.py       # 本地 GPU 可用性检查
└── test/
    └── test_chat_history.py # 单元测试
```

## 使用示例

*可视化*

```bash
python code/action_plot.py      # 绘制用户元行为散点图
python code/strategy_plot.py    # 绘制策略长度分布
```

*聊天评分*

```bash
python code/chat_history.py
```

*数据集构建*

```bash
python code/dataset/dataset.py        # 构建 SFT/KTO 数据集
python code/dataset/langsmith_data.py # 导出 LangSmith 运行记录
```

*模型评测*

```bash
python code/dataset/langsmith_eval.py
```

## 测试

运行单元测试：

```bash
pytest
```

## 许可

仓库未提供开源许可，如需传播请联系作者确认。

