import matplotlib.pyplot as plt
import pandas as pd

from mongodb.base import MongoDBClient

client = MongoDBClient()
think_df = pd.DataFrame(list(client.find_documents('yuhe', 'event_track', {'type': '思考策略'})))
think_df = pd.concat([think_df, pd.json_normalize(think_df.meta.tolist())], axis=1)
think_df['strategy_length'] = think_df['strategy'].apply(lambda x: len(x))
think_df['strategy_length'].plot(kind='hist', bins=100)
plt.show()
