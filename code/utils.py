from datetime import datetime, timedelta

import pandas as pd
import pytz

from action_plot import process_user_df
from mongodb.base import MongoDBClient

START_DATE = 20250506
END_DATE = int((datetime.today() - timedelta(days=4)).strftime('%Y%m%d'))
client = MongoDBClient()


def get_payment_corr(end_date: int = 20261118):
    user_df = process_user_df(end_date=end_date)
    print(user_df.shape)

    state_df = pd.json_normalize(user_df['chat_state'].apply(lambda x: x.get('state', {})))
    state_df = state_df.fillna(False).astype(int)
    # state_df = pd.concat([user_df, state_df])
    print(state_df.shape)

    corr_df = state_df.corr()
    payment_corr = corr_df['is_complete_payment']
    payment_corr = payment_corr.sort_values(ascending=False)
    return payment_corr

def select_state(user_state_df):
    if user_state_df.is_complete_payment.sum() == 0:
        return user_state_df.iloc[-1]
    else:
        return user_state_df[user_state_df.is_complete_payment == 1].iloc[0]

def get_chat_history_state_df(user_df):
    chat_history_df = pd.read_json(f'../data/yuhe/chat_history/yuhe.chat_history.json')
    chat_history_df = chat_history_df[chat_history_df.chat_id.isin(user_df.chat_id.tolist())].reset_index(drop=True)
    chat_history_df = pd.concat([chat_history_df, pd.json_normalize(chat_history_df.created_at.tolist())], axis=1)
    chat_history_df['shanghai'] = pd.to_datetime(chat_history_df['$date']).apply(lambda x: x.astimezone(pytz.timezone('Asia/Shanghai')))
    state_df = pd.json_normalize(chat_history_df.chat_state.fillna('').apply(lambda x: x.get('state', {}) if x else {}).tolist())
    chat_history_df = chat_history_df[['chat_id', 'shanghai', 'content', 'round_id', 'role', 'is_recalled', 'is_send_by_human']]
    return chat_history_df, state_df

def get_payment_corr_new(end_date: int = 20261118):
    user_df = process_user_df(end_date=end_date)
    user_df = user_df[-3000:]
    chat_history_df, state_df = get_chat_history_state_df(user_df)
    state_df = pd.concat([chat_history_df[['chat_id', 'shanghai']], state_df], axis=1)
    state_df = state_df.groupby('chat_id').apply(select_state)
    payment_corr = state_df.iloc[:, 3:].fillna(False).replace({True: 1, False: 0}).corr()['is_complete_payment']
    payment_corr['is_complete_payment'] = 0.2
    payment_corr = payment_corr.sort_values(ascending=False)
    return payment_corr


if __name__ == "__main__":
    payment_state_curr = get_payment_corr_new(END_DATE)
