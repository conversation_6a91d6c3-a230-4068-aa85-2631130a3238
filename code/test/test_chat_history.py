import ast
from pathlib import Path

import pandas as pd


def load_get_recall_reply_num():
    file_path = Path(__file__).resolve().parents[1] / "chat_history.py"
    module = ast.parse(file_path.read_text())
    func_node = next(
        n for n in module.body if isinstance(n, ast.FunctionDef) and n.name == "get_recall_reply_num"
    )
    temp_module = ast.Module(body=[func_node], type_ignores=[])
    code = compile(temp_module, filename=str(file_path), mode="exec")
    namespace = {"pd": pd}
    exec(code, namespace)
    return namespace["get_recall_reply_num"]


def test_get_recall_reply_num_without_payment():
    get_recall_reply_num = load_get_recall_reply_num()
    data = [
        {
            'is_complete_payment': False,
            'is_recalled': True,
            'is_send_by_human': False,
            'role': 'assistant',
        },
        {
            'is_complete_payment': False,
            'is_recalled': False,
            'is_send_by_human': True,
            'role': 'assistant',
        },
        {
            'is_complete_payment': False,
            'is_recalled': False,
            'is_send_by_human': False,
            'role': 'user',
        },
    ]
    df = pd.DataFrame(data)

    result = get_recall_reply_num(df)

    assert result['recalled'] == 1
    assert result['human_send'] == 1
    assert result['replied'] == 1
