import numpy as np
import pandas as pd

from mongodb.base import MongoDBClient

client = MongoDBClient()


def process_user_df(start_date: int = 20250506, end_date: int = 20261118):
    """
    从数据库中获取并清洗 user_df 数据。

    该函数使用模块级 `MongoDBClient` 实例 `client` 获取数据。

    参数:
    - start_date: 起始课程号
    - end_date: 结束课程号

    返回:
    - user_df: 清洗后的 user_df
    """
    ori_user_df = pd.DataFrame(list(client.find_documents('yuhe', 'chat', {})))
    ori_user_df = ori_user_df.rename(columns={'_id': 'chat_id', 'wx_id': 'ta_id'})
    ori_user_df = ori_user_df[ori_user_df.chat_id.str.startswith('7')].reset_index(drop=True)
    ori_user_df = ori_user_df[ori_user_df.ta_id != 'local']
    ori_user_df = ori_user_df.drop_duplicates(subset=['chat_id'])
    ori_user_df = ori_user_df[~ori_user_df.course_no.isna()]
    ori_user_df.course_no = ori_user_df.course_no.astype(int)
    ori_user_df = ori_user_df[ori_user_df.course_no >= start_date].reset_index(drop=True)

    phone_value_counts = ori_user_df.phone.value_counts()
    whitelist_phone = phone_value_counts[phone_value_counts > 2].index.tolist()
    ori_user_df = ori_user_df[~ori_user_df.phone.isin(whitelist_phone)]
    if end_date:
        ori_user_df = ori_user_df[ori_user_df.course_no < end_date]
    ori_user_df = ori_user_df[ori_user_df['created_at'] > pd.to_datetime('2025-05-06').to_datetime64()].reset_index(drop=True)
    ori_user_df['created_at'] = ori_user_df['created_at'].dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai')
    return ori_user_df


if __name__ == "__main__":
    # initialization
    DAY_NUM = 6
    START_DATE = 20250615
    END_DATE = 20250617  # DAY_NUM后运行

    # get raw data from db
    user_df = process_user_df(START_DATE, END_DATE)[['chat_id', 'created_at', 'course_no']]
    think_df = pd.DataFrame(list(client.find_documents('yuhe', 'event_track', {'type': '思考策略'})))
    manual_df = pd.DataFrame(list(client.find_documents('yuhe', 'event_track', {'type': '人工回复'})))
    print(user_df.shape)

    # cleaning for think_df
    think_df = think_df[think_df.chat_id.isin(user_df.chat_id)].reset_index(drop=True)
    think_df = pd.concat([think_df, pd.json_normalize(think_df.meta.tolist())], axis=1)
    think_df['timestamp'] = think_df['timestamp'].dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai')
    think_df = think_df[['chat_id', 'timestamp', 'action']]
    print(think_df.shape)

    # cleaning for manual_df
    manual_df = manual_df[manual_df.chat_id.isin(user_df.chat_id)].reset_index(drop=True)
    manual_df = manual_df.drop(columns=['type'])
    manual_df = pd.concat([manual_df, pd.json_normalize(manual_df.meta.tolist())], axis=1)
    manual_df['timestamp'] = manual_df['timestamp'].dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai')
    manual_df = manual_df[['chat_id', 'timestamp', 'type']]
    manual_df.type = manual_df.type.fillna('回复')
    manual_df.columns = ['chat_id', 'timestamp', 'action']
    print(manual_df.shape)

    # get actions
    actions = pd.DataFrame(think_df['action'].str.split(',').sum(), columns=['action'])
    actions['action_length'] = actions.action.apply(lambda x: len(x))
    actions = actions[(actions.action_length < 10) & (actions.action_length > 3)]

    import matplotlib.pyplot as plt
    from matplotlib import rcParams
    rcParams['font.sans-serif'] = ['Heiti TC']

    # scatter plot for actions with timeline
    think_df = pd.concat([think_df, manual_df], ignore_index=True)
    think_df = think_df.merge(user_df, on='chat_id', how='left')

    # Create figure with two subplots sharing y-axis
    fig, axs = plt.subplots(2, 2, sharey='row', sharex='col', figsize=(24, 24), gridspec_kw={'width_ratios': [1, 2],  'height_ratios': [2, 1]})
    ax_bar, ax_scatter = axs[0]
    _, ax_hist = axs[1]

    # Calculate action order by frequency across all data
    action_freq = actions.action.value_counts().index.tolist()
    action_order = {action: idx for idx, action in enumerate(action_freq)}

    action_list = []
    recall_list = []
    reply_list = []

    for chat_id in think_df.chat_id.unique():
        action_df = think_df[think_df.chat_id == chat_id]
        action_df = action_df.sort_values(by='timestamp').reset_index(drop=True)

        action_df['reply'] = False
        reply_indices = action_df.index[action_df['action'] == '回复']
        action_df.loc[np.clip(reply_indices - 1, 0, 300), 'reply'] = True
        action_df = action_df[action_df.action != '回复'].reset_index(drop=True)

        action_df['recall'] = False
        recall_indices = action_df.index[action_df['action'] == '撤回']
        action_df.loc[np.clip(recall_indices - 1, 0, 300), 'recall'] = True
        action_df = action_df[action_df.action != '撤回'].reset_index(drop=True)

        if action_df.empty:
            continue

        # Split the 'action' column by comma and expand into separate rows
        action_df.loc[:, 'action'] = action_df['action'].str.split(',')
        action_df = action_df.explode('action').reset_index(drop=True)
        action_df['action_length'] = action_df.action.apply(lambda x: len(x))
        action_df = action_df[(action_df.action_length < 10) & (action_df.action_length > 3)]

        # Increment timestamp by 1 second for each subsequent action based on the original timestamp
        action_df['timestamp'] = action_df.groupby('chat_id')['timestamp'].transform(lambda x: x + pd.to_timedelta(range(len(x)), unit='s'))

        # Calculate the time difference between 'timestamp' and 'created_at' date's midnight
        action_df['time_diff_days'] = (action_df['timestamp'] - action_df['created_at'].dt.normalize()).dt.total_seconds() / (24 * 60 * 60)
        action_df['action_order'] = action_df['action'].map(action_order)
        # ax_scatter.plot(test_df.time_diff_days, test_df.action_order, c='r', marker='o', lw=1, ms=2, alpha=0.1)
        ax_scatter.scatter(action_df.time_diff_days, action_df.action_order, c='g', s=30, alpha=0.1)
        action_list += action_df.time_diff_days.tolist()

        if action_df['recall'].sum() > 0:
            recall_df = action_df[action_df['recall']]
            ax_scatter.scatter(recall_df.time_diff_days, recall_df.action_order, c='r', s=60, alpha=0.5, marker='x')
            recall_list += recall_df.time_diff_days.tolist()

        if action_df['reply'].sum() > 0:
            reply_df = action_df[action_df['reply']]
            ax_scatter.scatter(reply_df.time_diff_days, reply_df.action_order, c='b', s=60, alpha=0.5, marker='x')
            reply_list += reply_df.time_diff_days.tolist()

    ax_scatter.set_xlim(0, DAY_NUM)
    ax_scatter.set_title('时间散点图')
    ax_scatter.set_xlabel('课程日')
    ax_scatter.grid()

    # Plot bar chart on the left sharing the same y-axis
    action_counts = actions.action.value_counts()
    bars = action_counts.plot(kind='barh', ax=ax_bar, color='g', logx=True, align='center')
    ax_bar.set_title('元行为频次')
    # ax_bar.set_xlabel('频次')
    ax_bar.invert_xaxis()
    ax_bar.yaxis.set_label_position("right")
    ax_bar.yaxis.tick_right()
    ax_bar.grid()
    for i, (index, value) in enumerate(action_counts.items()):
        ax_bar.text(value, i, str(value), va='bottom', c='r')

    # Plot histogram on the bottom-right
    ax_hist.hist(action_list, bins=DAY_NUM * 24, range=(0, DAY_NUM), color='g', alpha=0.2, label='元行为')
    ax_hist.hist(recall_list, bins=DAY_NUM * 24, range=(0, DAY_NUM), color='r', alpha=0.6, label='撤回')
    ax_hist.hist(reply_list, bins=DAY_NUM * 24, range=(0, DAY_NUM), color='b', alpha=0.4, label='回复')
    ax_hist.set_ylim([0, 50])
    ax_hist.legend()
    ax_hist.grid()

    plt.tight_layout()
    # plt.savefig(f'../plot/yuhe/meta_action/action_{START_DATE}_{END_DATE}_{user_df.shape[0]}.pdf', format='pdf')
    plt.show()
