import json
import os
import re

from dotenv import load_dotenv
from langchain.chat_models import init_chat_model
from langchain.schema.runnable import RunnableLambda
from langchain_core.messages import HumanMessage
from langsmith import Client, evaluate

def load_environment_vars(env_path='../../.env'):
    load_dotenv(env_path)
    os.environ["LANGCHAIN_API_KEY"] = os.getenv('LANGCHAIN_API_KEY')
    os.environ["OPENAI_API_KEY"] = os.getenv('CHEAP_API_KEY')

def init_model(
    name,
    provider,
    api_key_env,
    base_url_env=None,
    extra_body=None,
    reasoning_effort='minimal',  # minimal | low | medium | high
):
    extra = dict(extra_body or {})
    is_reasoning_model = name.startswith('gpt-5') and name != 'gpt-5-chat'

    if is_reasoning_model:
        # 只有推理模型才有 reasoning_effort / verbosity
        if reasoning_effort:
            extra["reasoning_effort"] = reasoning_effort
    else:
        # 普通模型支持采样参数
        extra.update({
            "temperature": 0.7,
            "max_tokens": 1024
        })
    kwargs = dict(
        model=name,
        model_provider=provider,
        api_key=os.getenv(api_key_env),
        base_url=os.getenv(base_url_env) if base_url_env else None,
        extra_body=extra
    )
    if provider == 'azure_openai':
        kwargs.update(
            azure_endpoint=os.getenv('AZURE_OPENAI_ENDPOINT'),
            api_version=os.getenv('AZURE_OPENAI_API_VERSION'),
            deployment_name=name
        )
    return init_chat_model(**kwargs)

def extract_xml_content(text, tag_name):
    pattern = rf"<{tag_name}>(.*?)</{tag_name}>"
    match = re.search(pattern, text, re.DOTALL)
    return match.group(1).strip() if match else ""

def safe_extract_strategy(ans: str) -> str:
    try:
        if '<strategy>' in ans:
            return extract_xml_content(ans, 'strategy')
        if '{' in ans:
            try:
                obj = json.loads(ans[ans.index('{'):])
                return obj.get('strategy', '')
            except json.JSONDecodeError:
                if  'strategy' in ans and 'content' in ans:
                    return ans[ans.index('strategy') + 12:ans.index('content') - 6]
                return ''
        return ''
    except Exception:
        return ''

def ranked_preference(inputs: dict, outputs: list[dict]) -> list:
    """
    Evaluate preference between two outputs based on the input question.
    Returns a list of scores indicating preference: [1,0], [0,1], or [0,0].
    """
    question = inputs.get('messages', [[{}]])[0][0].get("kwargs", {}).get("content", "")
    answer_a = outputs[0].get('content', "")
    answer_b = outputs[1].get('content', "")
    try:
        if '# 格式要求' in question:
            index = question.index('## 格式要求') if '## 格式要求' in question else question.index('# 格式要求')
            question = question[:index]
        elif '# 输出要求' in question:
            index = question.index('## 输出要求') if '## 输出要求' in question else question.index('# 输出要求')
            question = question[:index]
        index = question.index('## 元行为') if '## 元行为' in question else question.index('# 元行为')
        question = question[index:].strip()

        answer_a = safe_extract_strategy(answer_a)
        answer_b = safe_extract_strategy(answer_b)

        if not question or not answer_a or not answer_b:
            return [0, 0]
        response = eval_chain.invoke({"question": question, "answer_a":answer_a, "answer_b": answer_b})
        preference = response.get("preference", 0)
    except:
        print("Error:", question, answer_a, answer_b)
        preference = 0

    if preference == 1:
        scores = [1, 0]
    elif preference == 2:
        scores = [0, 1]
    else:
        scores = [0, 0]
    return scores

def exact_match(outputs: dict, reference_outputs: dict) -> bool:
    return outputs == reference_outputs

def input_mapper(data_sample: dict) -> list[HumanMessage]:
    """
    Extracts HumanMessage list from data sample for model input.
    """
    contents = [msg.get('kwargs', {}).get('content', 'N/A') for msg in data_sample.get('messages', [{}])[0]]
    return [HumanMessage(content=content) for content in contents]

def single_experiment(chain_name):
    evaluate(
        chain_name,
        data="Yuhe-FreeThink-20250812",
        experiment_prefix="qwen3-32b-kto-0814-0819",  # qwen3-32b-kto-0814-0819, gpt-5-minimal-0813, qwen-max
        max_concurrency=20,  # 20
    )

def pairwise_experiment(model_names, evaluators=None):
    evaluate(
        model_names,
        evaluators=evaluators,
        max_concurrency=20,
    )

if __name__ == "__main__":
    load_environment_vars()
    client = Client()
    model_name = 'gpt-5-mini'
    provider_name = 'azure'  # azure, cheap, local, qwen

    azure_model = init_model(model_name, "azure_openai", "AZURE_OPENAI_API_KEY")
    cheap_model = init_model(model_name, "openai", "CHEAP_API_KEY", "CHEAP_BASE_URL")
    local_model = init_model("qwen3-32b", "openai", "LOCAL_API_KEY", "LOCAL_BASE_URL")
    qwen_model = init_model("qwen-max-latest", "openai", "QWEN_API_KEY", "QWEN_BASE_URL", {"enable_thinking": False})  # qwen-max-latest

    eval_prompt_gpt = client.pull_prompt("pairwise-evaluation-free-think")
    eval_prompt = client.pull_prompt("pairwise-evaluation-free-think", include_model=True)  # prompt with model
    eval_chain = eval_prompt_gpt | azure_model

    model = {'azure': azure_model, 'cheap': cheap_model, 'local': local_model, 'qwen': qwen_model}.get(provider_name)
    chain = RunnableLambda(input_mapper) | model
    result = model.invoke("你好")
    print(result.response_metadata.get('model_name'), result.content)

    # single model eval
    # single_experiment(chain)
    # double model eval
    pairwise_experiment(('gpt-4.1-0813', 'qwen-max-0819'), evaluators=[ranked_preference])
