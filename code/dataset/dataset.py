import matplotlib.pyplot as plt
import pandas as pd

# init parameters
DATA_STAGE = 'sft'  # sft, kto
DATE = ''  # '_20250703'
LOWER_RATING = 0.06
MIDDLE_RATING = 0.1
UPPER_RATING = 0.2

rating_df = pd.read_json(f'../../data/yuhe/label_ratings/rating{DATE}.json', dtype={'chat_id': str})
runs_df = pd.read_json(f'../../data/yuhe/langsmith_runs/freethink{DATE}.json')
runs_df = runs_df[(runs_df.prompt_tokens <= 4096) & (runs_df.completion_tokens <= 512)]
print(rating_df.shape)
print(runs_df.shape)

rating_df.shanghai = pd.to_datetime(rating_df.shanghai, unit='ms')
runs_rating_df = runs_df.merge(rating_df, how='inner', on='round_id')
print(runs_rating_df.shape)

def content_replace(row):
    if row[0].startswith('<'):
        return row[0][:row[0].index('<content>')] + '<content>' + row[1] + '</content>'
    return row[0][:row[0].index('"content"')] + '"content": "' + row[1] + '"\n}'

# get the label
runs_rating_df['output'] = runs_rating_df[['output', 'content']].apply(content_replace, axis=1)
runs_rating_df['label'] = runs_rating_df.rating >= UPPER_RATING

if DATA_STAGE == 'sft':
    runs_rating_df = runs_rating_df[(runs_rating_df.rating < UPPER_RATING) & (runs_rating_df.rating >= MIDDLE_RATING)]
elif DATA_STAGE == 'kto':
    runs_rating_df = runs_rating_df[(runs_rating_df.rating <= LOWER_RATING) | (runs_rating_df.rating >= UPPER_RATING)]

runs_rating_df[['input', 'output'] if DATA_STAGE == 'sft' else ['input', 'output', 'label', 'rating']].to_json(
    f'../../data/yuhe/dataset/{DATA_STAGE}{DATE}.json', orient='records', force_ascii=False, indent=2)

print(runs_rating_df.shape)
print(runs_rating_df.label.sum() / runs_rating_df.shape[0])
plt.hist(runs_rating_df.rating, bins=100)
plt.yscale("log")
plt.grid()
plt.show()
