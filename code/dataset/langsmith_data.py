import os
import random
from collections import defaultdict
from datetime import datetime

import matplotlib.pyplot as plt
import pandas as pd
from dotenv import load_dotenv
from langsmith import Client
from tqdm import tqdm

load_dotenv('../../.env')
os.environ["LANGCHAIN_API_KEY"] = os.getenv('LANGCHAIN_API_KEY')
client = Client()

# filter runs to add to the dataset
start_time = datetime(2025, 5, 1)
end_time = datetime(2025, 12, 31, 23, 59, 59)

def output_runs_data(runs_list):
    runs_df = pd.DataFrame([{
        'input': run.inputs.get('messages', [[{}]])[0][0].get('kwargs', {}).get('content', ''),
        'output': run.outputs.get('generations', [[{}]])[0][0].get('text', '') if isinstance(run.outputs, dict) and run.outputs.get('generations') else '',
        'round_id': run.metadata.get('round_id', ''),
        'status': run.status,
        'prompt_tokens': run.prompt_tokens,
        'completion_tokens': run.completion_tokens
    } for run in runs_list])
    runs_df = runs_df[runs_df.status == 'success']
    runs_df = runs_df[runs_df.completion_tokens > 0]
    runs_df = runs_df.drop(columns=['status'])
    think_strategy_df = pd.read_json(f'../../data/yuhe/langsmith_runs/think_strategy.json')
    print(f"共获取 {think_strategy_df.shape[0]} 条 ThinkStrategy 调用记录")
    freethink_df = pd.concat([runs_df, think_strategy_df])
    print(f"共获取 {freethink_df.shape[0]} 条 LLM 调用记录")
    freethink_df.to_json('../../data/yuhe/langsmith_runs/freethink.json', orient='records', force_ascii=False, indent=2)

def create_dataset(runs_list):
    dataset_name = "Yuhe-FreeThink-20250812"
    if client.has_dataset(dataset_name=dataset_name):
        client.read_dataset(dataset_name)
        print(f"数据集已存在，使用已有数据集: {dataset_name}")
    else:
        client.create_dataset(dataset_name)
        print(f"创建新数据集: {dataset_name}")

    prompt_tokens_array = pd.Series([run.prompt_tokens for run in runs_list])
    completion_tokens_array = pd.Series([run.completion_tokens for run in runs_list])
    plt.hist(prompt_tokens_array, bins=100)
    plt.show()
    plt.hist(completion_tokens_array, bins=100)
    plt.show()

    # Step 1: 过滤 prompt_tokens ∈ [512, 4096]
    filtered_runs = [run for run in runs_list if 512 <= run.prompt_tokens <= 4096]

    # Step 2: 统计 chat_id 对应的 runs
    chat_id_to_runs = defaultdict(list)
    for run in filtered_runs:
        chat_id = run.metadata.get('chat_id', None)
        if chat_id:
            chat_id_to_runs[chat_id].append(run)

    # Step 3: 按 chat_id 筛选 runs
    final_runs = []
    for chat_id, runs in chat_id_to_runs.items():
        if len(runs) <= 10:
            continue  # 跳过数量太少的 chat_id
        elif len(runs) > 10:
            final_runs.extend(random.sample(runs, 5))  # 随机选取10条
        else:
            final_runs.extend(runs)  # 全部保留

    print(f"最终筛选后共 {len(final_runs)} 条 run 被保留用于评估集")

    # Prepare inputs and outputs for bulk creation
    examples = [{"inputs": run.inputs, "outputs": run.outputs} for run in final_runs]

    # Use the bulk create_examples method
    for i in tqdm(range(0, len(examples), 100)):
        client.create_examples(
            dataset_name=dataset_name,
            examples=examples[i:i+100]
        )


if __name__ == "__main__":
    # # old version of freethink
    # think_strategy = list(client.list_runs(
    #     project_name='yuhe', run_type="llm", error=False, start_time=start_time, end_time=end_time,
    #     filter='and(eq(run_type, "llm"), and(eq(metadata_key, "promptName"), eq(metadata_value, "think_strategy")))',
    #     # select=['inputs', 'outputs', 'metadata', 'status', 'prompt_tokens', 'completion_tokens'],
    # ))
    # print(f"共获取 {len(think_strategy)} 条 ThinkStrategy 调用记录")

    free_think = list(client.list_runs(
        project_name='yuhe', run_type="llm", error=False, start_time=start_time, end_time=end_time,
        filter='and(eq(run_type, "llm"), and(eq(metadata_key, "promptName"), eq(metadata_value, "free_think")))',
        # select=['inputs', 'outputs', 'metadata', 'status', 'prompt_tokens', 'completion_tokens'],
    ))
    print(f"共获取 {len(free_think)} 条 FreeThink 调用记录")

    # all_runs_list = free_think + think_strategy
    # print(f"共获取 {len(all_runs_list)} 条 LLM 调用记录")

    # create_dataset(free_think)
    output_runs_data(free_think)
