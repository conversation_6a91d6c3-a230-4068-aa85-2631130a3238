from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig

# ../../../model/Qwen/Qwen3-32B
# model_name = "Qwen/Qwen3-32B"
model_name = "Qwen/Qwen3-32B"  # Qwen3-32B, Qwen3-30B-A3B-Instruct-2507
cache_dir = '../../model/'

# download the model
from modelscope import snapshot_download
model_dir = snapshot_download(model_name, cache_dir=cache_dir)

def get_model_output(prompt="Give me a short introduction to large language model."):
    # prepare the model input
    messages = [
        {"role": "user", "content": prompt}
    ]
    text = tokenizer.apply_chat_template(
        messages,
        tokenize=False,
        add_generation_prompt=True,
        enable_thinking=False # Switches between thinking and non-thinking modes. Default is True.
    )
    model_inputs = tokenizer([text], return_tensors="pt").to(model.device)

    # conduct text completion
    generated_ids = model.generate(
        **model_inputs,
        max_new_tokens=32768
    )
    output_ids = generated_ids[0][len(model_inputs.input_ids[0]):].tolist()

    # parsing thinking content
    try:
        # rindex finding 151668 (</think>)
        index = len(output_ids) - output_ids[::-1].index(151668)
    except ValueError:
        index = 0

    thinking_content = tokenizer.decode(output_ids[:index], skip_special_tokens=True).strip("\n")
    content = tokenizer.decode(output_ids[index:], skip_special_tokens=True).strip("\n")

    print("thinking content:", thinking_content)
    print("content:", content)


if __name__ == "__main__":
    # load the tokenizer and the model
    model_name = cache_dir + model_name
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_compute_dtype='float16',  # 计算时的数据类型
        bnb_4bit_use_double_quant=True,  # 使用双量化进一步压缩
        bnb_4bit_quant_type="nf4",  # NF4 类型量化
    )
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        # torch_dtype="auto",
        device_map="auto",
        quantization_config=bnb_config,
    )

    # get_model_output('你是个傻逼')
