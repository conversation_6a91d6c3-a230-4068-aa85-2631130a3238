import base64
import json

# 读取 base64 编码的 txt 文件
ori_data = '../../data/haogu/chat_history_human/1688858251490982_7881301894024444.txt'
with open(ori_data, 'r') as f:
    base64_content = f.read()

# 解码为原始内容（bytes）
decoded_bytes = base64.b64decode(base64_content)

# 如果内容是字符串，就再转为 str
decoded_text = decoded_bytes.decode('utf-8')
print(decoded_text)

decoder = json.JSONDecoder()
objs = []
idx = 0
while idx < len(decoded_text):
    obj, end = decoder.raw_decode(decoded_text[idx:])
    objs.append(obj)
    idx += end
