import json
import pandas as pd

# read original chat history data
with open("../../data/more/chat_history_human/currentMsg_20250623.txt", "r", encoding="utf-8") as f:
    data = json.load(f)
chat_history_df = pd.DataFrame(data)
print(chat_history_df.shape)

chat_history_df['user_id_length'] = chat_history_df.user_from.apply(lambda x: len(x))
ta_chat_count = chat_history_df[chat_history_df.user_id_length < 15].user_from.value_counts()
chat_history_df['teacher'] = chat_history_df.user_id_length < 15
chat_history_df['chat_id'] = chat_history_df[['user_from', 'user_to']].apply(lambda x: min(x[0], x[1]) + '_' + max(x[0], x[1]), axis=1)
chat_history_df = chat_history_df.sort_values(by=['chat_id', 'msg_date']).reset_index(drop=True)
# chat_history_df[['user_from', 'user_to', 'teacher', 'msg_date', 'content']].to_excel("../data/more/chat_history_human/chat_history_20250624.xlsx")
