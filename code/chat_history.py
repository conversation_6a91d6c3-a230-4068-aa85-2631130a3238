import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

from action_plot import process_user_df
from utils import get_chat_history_state_df, get_payment_corr_new, START_DATE, END_DATE

pd.set_option('future.no_silent_downcasting', True)

MIN_USER_MSG_COUNT = 3
SECONDS_PER_MINUTE = 60
SECONDS_PER_HOUR = 60 * 60
SECONDS_PER_DAY = 24 * SECONDS_PER_HOUR
MIN_RATING = -0.4
MAX_RATING = 1.0
CHOSEN_STATES = ['is_complete_payment', 'is_complete_course_day', 'is_attend_course_day', 'is_complete_homework', 'is_complete_douyin_analysis']
STATES_CONFIG = {
    'is_complete_payment': {'round': 32, 'time': 2 * SECONDS_PER_DAY},
    'is_complete_course_day': {'round': 16, 'time': 1 * SECONDS_PER_DAY},
    'is_attend_course_day': {'round': 8, 'time': 12 * SECONDS_PER_HOUR},
    'is_complete_homework': {'round': 4, 'time': 3 * SECONDS_PER_HOUR},
    'is_complete_douyin_analysis': {'round': 4, 'time': 3 * SECONDS_PER_HOUR},
}

# user data initialization
user_df = process_user_df(START_DATE, END_DATE)
user_df['user_msg_count'] = user_df.chat_state.apply(lambda x: x.get('nodeInvokeCount', 0).get('UserMessage', 0))
user_df['is_complete_payment'] = user_df.chat_state.apply(lambda x: x.get('state', False).get('is_complete_payment', False))
# plt.hist(user_df['user_msg_count'], range=(0, 100), bins=50, alpha=0.5)
# plt.hist(user_df[user_df.is_complete_payment]['user_msg_count'], range=(0, 100), bins=50, alpha=0.5)
# plt.yscale('log')
# plt.show()
user_df = user_df[user_df.user_msg_count >= MIN_USER_MSG_COUNT]

# get chat history and clean the data
chat_history_df, state_df = get_chat_history_state_df(user_df)

# # get content length for the user message
# chat_history_df_user = chat_history_df[chat_history_df.role == 'user']
# chat_history_df_user['content_length'] = chat_history_df_user.content.apply(lambda x: len(x))
# chat_history_df_user['content_list'] = chat_history_df_user.content.apply(lambda x: x.split('\n'))
# content_df = pd.DataFrame(chat_history_df_user.content_list.explode())
# content_df['content_length'] = content_df.content_list.apply(lambda x: len(x))

# concat the chat history and state
filtered_columns = sorted([col for col in state_df.columns if any(col.startswith(prefix) for prefix in CHOSEN_STATES)])
state_df = state_df[filtered_columns]
chat_history_state_df = pd.concat([chat_history_df, state_df], axis=1)


def get_recall_reply_num(one_user_df):
    payment_indices = one_user_df[one_user_df['is_complete_payment'] == True].index
    if not payment_indices.empty:
        first_payment_index = payment_indices.min()
        one_user_df = one_user_df.loc[:first_payment_index - 1]
    return pd.Series({
        'recalled': int(one_user_df.is_recalled.sum()),
        'human_send': int(one_user_df.is_send_by_human.sum()),
        # Use a default value of 0 when there are no user messages to avoid returning None
        'replied': int(one_user_df.role.value_counts().get('user', 0))})


# get feature weight for user states and messages
payment_state_curr = get_payment_corr_new(END_DATE)
weight_states = payment_state_curr[filtered_columns]
is_recalled_send_by_human_df = chat_history_state_df.groupby('chat_id').apply(get_recall_reply_num).reset_index()
user_df = user_df.merge(is_recalled_send_by_human_df, on='chat_id', how='left')
payment_message_curr = user_df.iloc[:, -4:].corr()
print(payment_message_curr)

weight_recalled = -payment_message_curr.loc['recalled', 'is_complete_payment']
weight_human_send = -payment_message_curr.loc['human_send', 'is_complete_payment']
weight_replied = payment_message_curr.loc['replied', 'is_complete_payment']
long_dfs = []
think_nums = []


def log_scaled_score_array(delta_t_seconds_array, max_t=3600):
    """
    根据时间间隔数组计算评分（范围0～1）：
    - delta_t_seconds_array: numpy数组，单位为秒
    - max_t: 最大间隔时间，超过这个值统一视为0分
    """
    t = np.clip(delta_t_seconds_array, 0, max_t)
    score = 1 - np.log1p(t) / np.log1p(max_t)
    return np.clip(score, 0, 1)


def get_think_ratings(one_user_df):
    # one user analysis
    one_user_df.loc[:, filtered_columns] = one_user_df[filtered_columns].apply(lambda x: x.where(x.cumsum() == 1, np.nan))
    new_user_df = one_user_df[(one_user_df.role == 'user') | one_user_df.round_id.isna()]
    assistant_df = one_user_df[(one_user_df.role == 'assistant') & one_user_df.round_id.notna()]
    freetalk_df = assistant_df.groupby('round_id').agg(
        {'chat_id': 'first', 'shanghai': 'min', 'content': lambda x: ' '.join(x), 'role': 'first'} |
        {col: 'sum' for col in one_user_df.columns[5:]}).reset_index()
    new_user_df = pd.concat([new_user_df, freetalk_df], axis=0).sort_values(by='shanghai').reset_index(drop=True)
    new_user_df.iloc[:, 5:] = new_user_df.iloc[:, 5:].fillna(0).astype(int).clip(0, 1)

    # calculate ratings for recall and manual reply
    new_user_df.is_recalled *= weight_recalled
    assistant_df = new_user_df[new_user_df.role == 'assistant'].reset_index(drop=True)
    assistant_df['time_diff'] = assistant_df.shanghai.diff().dt.total_seconds() < SECONDS_PER_HOUR
    assistant_df.is_send_by_human *= assistant_df.time_diff
    assistant_df.is_send_by_human = assistant_df.is_send_by_human.shift(-1).fillna(0)
    assistant_df.is_send_by_human *= weight_human_send

    # calculate ratings for user behaviors
    for column in filtered_columns:
        if assistant_df[column].sum() == 0:
            continue
        for state in CHOSEN_STATES:
            if column.startswith(state):
                state_config = STATES_CONFIG.get(state, {})
                state_index = assistant_df[assistant_df[column] > 0].index[0]
                target_time = assistant_df[assistant_df[column] > 0]['shanghai'].iloc[0]
                assistant_df['time_diff'] = (target_time - assistant_df['shanghai']).dt.total_seconds()
                assistant_df['time_diff'] = (0 < assistant_df['time_diff']) & (
                    assistant_df['time_diff'] < state_config.get('time', 0)
                )
                assistant_df.loc[state_index - state_config.get('round', 0):state_index, column] = 1
                assistant_df[column] *= assistant_df.time_diff
                assistant_df[column] *= weight_states[column]
    assistant_df = assistant_df.drop(columns=['time_diff'])

    # calculate ratings for user message
    new_user_df = pd.concat([new_user_df[new_user_df.role == 'user'], assistant_df], axis=0).sort_values(by='shanghai').reset_index(drop=True)
    new_user_df['time_diff'] = new_user_df.shanghai.diff().dt.total_seconds()
    new_user_df['replied'] = new_user_df.role.apply(lambda x: 1 if x == 'user' else 0)
    new_user_df.replied *= log_scaled_score_array(new_user_df.time_diff) * weight_replied
    new_user_df.replied = new_user_df.replied.shift(-1).fillna(0)
    new_user_df = new_user_df.drop(columns=['time_diff'])

    # get the final ratings
    for col in new_user_df.iloc[:, 5:].columns:
        new_user_df[col] = pd.to_numeric(new_user_df[col], errors='coerce').fillna(0.0)
    new_user_df['rating'] = new_user_df.iloc[:, 5:].sum(axis=1)
    assistant_df = new_user_df[(new_user_df.role == 'assistant') & new_user_df.round_id.notna()]

    # rating plot
    think_nums.append(assistant_df.shape[0])
    if assistant_df.shape[0] > 180:
        user_rating_df = assistant_df.iloc[:, 5:-1].reset_index(drop=True)
        long_dfs.append(user_rating_df[[user_rating_df.columns[-1]] + list(user_rating_df.columns[:-1])])
    return assistant_df[['shanghai', 'round_id', 'content', 'rating']]


def plot_rating_bar(plot_dfs):
    # rating bar plot for some users
    fig, axes = plt.subplots(nrows=len(plot_dfs), ncols=1, figsize=(18, 6 * len(plot_dfs)))
    for ax, df in zip(axes, plot_dfs):
        if 'rating' in df.columns:
            df = df.drop(columns=['rating'])
        df.plot.bar(stacked=True, ax=ax, ylim=(MIN_RATING, MAX_RATING))
        df['rating'] = df.sum(axis=1)
        df.rating.plot(ax=ax, color='red')
        ax.grid(True)
    plt.tight_layout()
    plt.show()


# output rating file
rating_df = chat_history_state_df.groupby('chat_id').apply(get_think_ratings).reset_index()
rating_df = rating_df.drop(columns=['level_1'])
think_nums = np.array(think_nums)
rating_df.to_json('../data/yuhe/label_ratings/rating.json', orient='records', force_ascii=False, indent=2)
# mean_rating_df = rating_df.groupby('chat_id').agg({'rating': 'mean', 'label': 'first'}).reset_index()
# print(mean_rating_df.corr())

plt.hist(rating_df.rating, bins=100, range=(MIN_RATING, MAX_RATING))
plt.yscale("log")
plt.grid()
plt.show()
