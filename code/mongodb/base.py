import os
from pymongo import MongoClient
from dotenv import load_dotenv

class MongoDBClient:
    def __init__(self):
        # load .env file
        load_dotenv()
        self.host = os.getenv('MONGO_HOST', 'localhost')
        self.port = int(os.getenv('MONGO_PORT', 27017))
        self.client = MongoClient(self.host, self.port)

    def get_database(self, db_name):
        return self.client[db_name]

    def get_collection(self, db_name, collection_name):
        db = self.get_database(db_name)
        return db[collection_name]

    def find_documents(self, db_name, collection_name, query=None):
        if query is None:
            query = {}
        collection = self.get_collection(db_name, collection_name)
        return collection.find(query)
