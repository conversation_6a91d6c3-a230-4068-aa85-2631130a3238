# Free-Train

Free-Train collects utilities and data processing scripts for building and evaluating **Free Think** conversational models.  The code focuses on pulling chat records from MongoDB, rating user interactions, and constructing supervised fine-tuning (SFT) or KTO datasets.

## Requirements

1. Python 3.10+
2. [MongoDB](https://www.mongodb.com/) instance with chat data
3. Optional LangSmith and model provider credentials stored in a `.env` file

Install dependencies:

```bash
pip install -r requirements.txt
```

## Project Structure

```
code/
├── action_plot.py          # Clean user data and visualize action timelines
├── chat_history.py         # Merge chat logs with state labels and compute ratings
├── strategy_plot.py        # Histogram of think-strategy length
├── utils.py                # Helper functions for correlation and chat history
├── mongodb/
│   ├── base.py             # Thin wrapper around MongoDB client
│   └── get_data_from_db.py # Example query helper
├── dataset/
│   ├── dataset.py          # Build SFT/KTO datasets from LangSmith runs
│   ├── get_model.py        # Download Qwen models and run generation
│   ├── langsmith_data.py   # Export LangSmith run data to JSON
│   ├── langsmith_eval.py   # Evaluate models with <PERSON><PERSON><PERSON> pairwise comparison
│   ├── load_human_data.py  # Preprocess human chat history
│   ├── cleaning.py         # Decode base64 chat archives
│   └── local_chat.py       # Quick GPU availability check
└── test/
    └── test_chat_history.py # Unit tests
```

## Usage

*Visualisation*

```bash
python code/action_plot.py      # scatter plots for user actions
python code/strategy_plot.py    # histogram of strategy lengths
```

*Chat history rating*

```bash
python code/chat_history.py
```

*Dataset preparation*

```bash
python code/dataset/dataset.py        # create SFT or KTO dataset
python code/dataset/langsmith_data.py # export LangSmith runs
```

*Model evaluation*

```bash
python code/dataset/langsmith_eval.py
```

## Testing

Run the included unit test:

```bash
pytest
```

## License

No license file is present; check with the repository owner before distributing.

